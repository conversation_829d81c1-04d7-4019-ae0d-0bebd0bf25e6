import React, { useState, useEffect } from 'react';

interface PerformanceMetrics {
  renderCount: number;
  lastRenderTime: number;
  averageRenderTime: number;
  memoryUsage?: number;
}

const PerformanceMonitor: React.FC<{ componentName: string }> = ({ componentName }) => {
  const [metrics, setMetrics] = useState<PerformanceMetrics>({
    renderCount: 0,
    lastRenderTime: 0,
    averageRenderTime: 0
  });
  const [renderTimes, setRenderTimes] = useState<number[]>([]);

  useEffect(() => {
    const startTime = performance.now();
    
    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime;
      
      setRenderTimes(prev => {
        const newTimes = [...prev, renderTime].slice(-10); // Keep last 10 renders
        const average = newTimes.reduce((sum, time) => sum + time, 0) / newTimes.length;
        
        setMetrics(prevMetrics => ({
          renderCount: prevMetrics.renderCount + 1,
          lastRenderTime: renderTime,
          averageRenderTime: average,
          memoryUsage: (performance as any).memory?.usedJSHeapSize || undefined
        }));
        
        return newTimes;
      });
    };
  });

  // Log performance issues
  useEffect(() => {
    if (metrics.renderCount > 5 && metrics.averageRenderTime > 16) {
      console.warn(`⚠️ Performance Warning: ${componentName} is re-rendering frequently`, {
        renderCount: metrics.renderCount,
        averageRenderTime: metrics.averageRenderTime.toFixed(2) + 'ms'
      });
    }
  }, [metrics, componentName]);

  if (process.env.NODE_ENV !== 'development') {
    return null; // Only show in development
  }

  return (
    <div className="fixed bottom-4 right-4 bg-black bg-opacity-80 text-white p-3 rounded-lg text-xs font-mono z-50">
      <div className="font-bold text-yellow-400">{componentName}</div>
      <div>Renders: {metrics.renderCount}</div>
      <div>Last: {metrics.lastRenderTime.toFixed(2)}ms</div>
      <div>Avg: {metrics.averageRenderTime.toFixed(2)}ms</div>
      {metrics.memoryUsage && (
        <div>Memory: {(metrics.memoryUsage / 1024 / 1024).toFixed(1)}MB</div>
      )}
      {metrics.renderCount > 10 && (
        <div className="text-red-400">⚠️ High render count</div>
      )}
    </div>
  );
};

export default PerformanceMonitor;
