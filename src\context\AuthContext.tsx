import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase, type Profile } from '../lib/supabase';
import type { User } from '@supabase/supabase-js';

interface AuthContextType {
  user: (User & { profile?: Profile }) | null;
  profile: Profile | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, fullName: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
  isAdmin: boolean;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<(User & { profile?: Profile }) | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Get initial session
    supabase.auth.getSession().then(({ data: { session } }) => {
      if (session?.user) {
        setUser(session.user);
        // Fetch profile in background - don't block initial load
        fetchProfile(session.user.id).catch(err => {
          console.error('Initial profile fetch failed:', err);
        });
      }
      setLoading(false);
    });

    // Listen for auth changes with debouncing
    let authChangeTimeout: NodeJS.Timeout;
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log('🔄 Auth state changed:', { event, hasSession: !!session, userId: session?.user?.id });

      // Clear previous timeout to debounce rapid changes
      if (authChangeTimeout) {
        clearTimeout(authChangeTimeout);
      }

      // Debounce auth state changes
      authChangeTimeout = setTimeout(() => {
        if (session?.user) {
          setUser(session.user);
          // Fetch profile in background - don't block auth state update
          fetchProfile(session.user.id).catch(err => {
            console.error('Profile fetch failed in background:', err);
          });
        } else {
          setUser(null);
          setProfile(null);
        }
        setLoading(false);
        console.log('✅ Auth state update completed');
      }, 100); // 100ms debounce
    });

    return () => subscription.unsubscribe();
  }, []);

  const [fetchingProfile, setFetchingProfile] = useState<string | null>(null);

  const fetchProfile = async (userId: string) => {
    // Prevent multiple simultaneous fetches for the same user
    if (fetchingProfile === userId) {
      console.log('👤 Profile fetch already in progress for:', userId);
      return;
    }

    console.log('👤 Fetching profile for user:', userId);
    setFetchingProfile(userId);

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('❌ Profile fetch failed:', error);

        // If profile doesn't exist, try to create a basic one
        if (error.code === 'PGRST116' || error.message?.includes('No rows')) {
          console.log('📝 Profile not found, creating basic profile...');

          try {
            const currentUser = await supabase.auth.getUser();
            const { data: newProfile, error: createError } = await supabase
              .from('profiles')
              .insert({
                id: userId,
                email: currentUser.data.user?.email || '',
                full_name: currentUser.data.user?.user_metadata?.full_name || '',
                role: 'user'
              })
              .select()
              .single();

            if (createError) {
              console.error('❌ Failed to create profile:', createError);
              setProfile(null);
            } else {
              console.log('✅ Profile created successfully:', newProfile);
              setProfile(newProfile);
            }
          } catch (createErr) {
            console.error('❌ Profile creation failed:', createErr);
            setProfile(null);
          }
        } else {
          console.error('❌ Profile fetch error (not missing profile):', error);
          setProfile(null);
        }
        return;
      }

      console.log('✅ Profile fetched successfully:', data);
      setProfile(data);

    } catch (error) {
      console.error('💥 Unexpected error fetching profile:', error);
      setProfile(null);
    } finally {
      setFetchingProfile(null);
    }
  };

  const login = async (email: string, password: string): Promise<void> => {
    console.log('🔐 Attempting to login user:', { email });

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('❌ Login failed:', error);
      throw error;
    }

    console.log('✅ Login successful:', {
      user: data.user?.id,
      email: data.user?.email,
      session: !!data.session
    });
  };

  const register = async (email: string, password: string, fullName: string): Promise<void> => {
    console.log('🔐 Attempting to register user:', { email, fullName });

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName
        }
      }
    });

    if (error) {
      console.error('❌ Registration failed:', error);
      throw error;
    }

    console.log('✅ Registration successful:', {
      user: data.user?.id,
      email: data.user?.email,
      needsConfirmation: !data.session
    });

    if (!data.session) {
      console.log('📧 Email confirmation required');
    }
  };

  const logout = () => {
    supabase.auth.signOut();
  };

  const value = {
    user,
    profile,
    login,
    register,
    logout,
    loading,
    isAuthenticated: !!user,
    isAdmin: profile?.role === 'admin'
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};